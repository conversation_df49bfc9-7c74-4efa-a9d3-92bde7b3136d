/*
 * nvrsgwcalcres.c - NVR解码资源分配计算模块
 * 
 * 功能说明：
 * 本模块实现了一个复杂的2D位图资源分配算法，用于NVR解码器的资源管理
 * 主要特点：
 * 1. 采用二级资源管理：主位图(32x12) + 特殊像素级资源映射
 * 2. 智能清理机制：特殊像素级释放时自动处理对应主位图单元
 * 3. 资源重排算法：支持动态碎片整理和资源优化分配
 * 4. 多通道支持：支持4个解码通道的并行资源管理
 * 5. 音视频资源统一管理：同时处理视频解码和音频解码资源
 *
 *  Created on: 2021年1月28日
 *      Author: kdm_work
 */

#include "debuglog.h"
#include "nvrsgwcore.h"
#include "nvrsgwcommon.h"
#include "nvrsys_in.h"
#include <pthread.h>


/* 
 * 核心宏定义 - 资源位图管理系统
 */
#define RES_BITMAP_MASK             (0xFFFFFFFF)    // 位图满掩码，表示所有位都可用

// 位图维度定义：32宽x12高的2D资源空间
#define RES_BITMAP_LEN              32          // 位图宽度：每行32个资源单位 (NVR_SGW_MAX_RES_WIDTH / RES_WIDTH_UNIT)
#define RES_BITMAP_MAX_NUM          12          // 位图高度：共12行资源 (NVR_SGW_MAX_RES_HEIGHT / RES_HEIGHT_UNIT)

// 核心掩码生成宏：生成n个连续1的掩码
// 例如：UNIT_LEN_MASK(4) = 0x0000000F (二进制：1111)
// 例如：UNIT_LEN_MASK(3) = 0x00000007 (二进制：111)
#define UNIT_LEN_MASK(n)            ((((unsigned long long)0x1 << n) - 1) & 0xffffffff)

// 像素坐标到资源单位的转换宏
#define GET_RES_WIDTH_BASE(n)       ((n) / RES_WIDTH_UNIT)         // 获取完整的宽度单位数
#define GET_RES_WIDTH_LEFT(n)       ((n) % RES_WIDTH_UNIT)         // 获取剩余的像素数
#define GET_RES_WIDTH_UNIT(n)       ((n + RES_WIDTH_UNIT - 1) / RES_WIDTH_UNIT)   // 向上取整的单位数
#define GET_RES_HEIGHT_UNIT(n)      ((n + RES_HEIGHT_UNIT - 1) / RES_HEIGHT_UNIT) // 向上取整的高度单位数

// 关键掩码计算宏：生成从右边开始的n位掩码，并左移到指定位置
// diff参数：起始位置偏移，用于精确定位资源在位图中的位置
// 例如：CHECK_MASK(4, 8) 生成4位掩码并左移到第8位位置开始
#define CHECK_MASK(n, diff)         (UNIT_LEN_MASK(n) << (RES_BITMAP_LEN - n - diff))

// 位操作辅助宏：获取指定位的值（0或1）
#define MASK_GET_BIT(x, bit)        ((x & (1 << (bit))) >> (bit))   /* 获取第bit位的值 */

/*
 * 资源位图信息枚举 - 用于描述可用资源区间的各种属性
 */
typedef enum
{
    RES_BITMAP_INFO_LEN = 0,            // 连续可用单位的长度（多少个连续的资源单位）
    RES_BITMAP_INFO_POS,                // 连续可用单位的起始索引位置（从左往右的位置）
    RES_BITMAP_INFO_BEGIN,              // 起始边界的额外像素长度（亚单位精度）
    RES_BITMAP_INFO_END,                // 结束边界的额外像素长度（亚单位精度）
    RES_BITMAP_INFO_NUM,                // 信息项总数（数组维度）
}ENvrSgwabyBitMapInfo;

/*
 * 资源空闲信息结构 - 描述单个可用资源块的详细信息
 */
typedef struct
{
    u8 byPos;       // 起始位置索引
    u8 byUnit;      // 资源单位数量 
    u8 byBlen;      // 起始边界额外长度（像素级精度）
    u8 byElen;      // 结束边界额外长度（像素级精度）
}TNvrSgwResFreeInfo;

/*
 * 可用资源信息集合 - 管理所有候选资源区域
 */
typedef struct
{
    s32 nArrayCount;                                            // 候选资源区域总数
    u8 abyBitMapInfo[RES_BITMAP_LEN][RES_BITMAP_INFO_NUM];     // 位图信息矩阵：32个位置×4种信息
    TNvrSgwResFreeInfo tFreeInfo[RES_BITMAP_LEN];              // 空闲资源详情数组
}TNvrSgwResAvailableInfo;

/*
 * 通道资源使用信息 - 记录单个通道当前占用的所有资源
 */
typedef struct
{
    s32 nArrayCount;        // 当前通道已分配的资源块总数
    s32 nArrayAudCount;     // 当前通道已分配的音频资源数（音频资源需要特殊计数）
    // 资源位置信息数组：视频+音频资源的混合数组
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_CHN_VID_NUM + NVR_SGW_MAX_CHN_AUD_NUM];
}TNvrSgwResUseInfo;

/*
 * 芯片级资源使用信息 - 记录整个芯片（多通道）的资源分配情况
 */
typedef struct
{
    s32 nArrayCount;        // 芯片级已分配资源总数
    // 芯片级资源数组：所有视频+音频资源
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_VID_NUM + NVR_SGW_MAX_AUD_NUM];
}TNvrSgwResAllUseInfo;


/*
 * ==================== 全局资源管理数组 ====================
 * 这些全局变量构成了整个资源管理系统的核心数据结构
 */

// 通道级资源使用映射表 - 记录每个解码通道的资源分配情况
TNvrSgwResUseInfo g_atResUseMap[NVR_SGW_MAX_CHN_NUM];

// 芯片级资源使用信息 - 按芯片统计资源使用，用于负载均衡和资源重排
TNvrSgwResAllUseInfo g_atStreamUseInfo[NVR_SGW_MAX_CHIP_CHN_NUM];

// 主资源位图 - 核心2D位图：[通道][高度行] = 32位掩码
// 位图中1表示可用，0表示已占用
u32 g_adwResMap[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM] = {0};

// 特殊像素级资源映射 - 三维数组：[通道][高度行][宽度位置] = 像素占用信息
// 用于处理不规整分辨率（如1280x720在单位化后的剩余像素）
// 低8位：起始边界占用像素数，高8位：结束边界占用像素数
u32 g_adwResMapSpe[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM][RES_BITMAP_LEN] = {0};

// 音频解码器ID分配位图 - 32位掩码，每位代表一个音频解码器
// 1表示可用，0表示已占用
u32 g_dwAudDecIdMap = 0xffffffff;

// 视频解码器ID分配位图 - 按芯片分组，每芯片管理16个解码器
u32 g_adwVidDecIdMap[NVR_SGW_MAX_CHIP_CHN_NUM] = {0xffffffff, 0xffffffff};

// 视频窗口ID分配位图 - 按通道分组，每通道管理16个显示窗口
u16 g_adwVidDecWinMap[NVR_SGW_MAX_CHN_NUM] = {0xffff, 0xffff, 0xffff, 0xffff};

/*
 * 备份数组 - 用于资源重排时的状态保存和恢复
 * 当资源重排失败时，可以快速恢复到之前的稳定状态
 */
TNvrSgwResUseInfo g_atResUseMap_bk[NVR_SGW_MAX_CHN_NUM];
TNvrSgwResAllUseInfo g_atStreamUseInfo_bk[NVR_SGW_MAX_CHIP_CHN_NUM];
u32 g_adwResMap_bk[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM] = {0};
u32 g_adwResMapSpe_bk[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM][RES_BITMAP_LEN] = {0};
u32 g_dwAudDecIdMap_bk = 0xffffffff;
u32 g_adwVidDecIdMap_bk[NVR_SGW_MAX_CHIP_CHN_NUM] = {0xffffffff, 0xffffffff};
u16 g_adwVidDecWinMap_bk[NVR_SGW_MAX_CHN_NUM] = {0xffff, 0xffff, 0xffff, 0xffff};

// 资源申请互斥锁 - 保证多线程环境下资源分配的原子性
static pthread_mutex_t  res_apply_lock;

// 调试打印级别控制 - 控制不同详细程度的调试信息输出
static s32 g_nBitmapPrint = 1;

// 前向声明
s32 NvrSgwFreeResToBitMap(s32 nChn, u16 x, u16 y, u16 nUnitLen, u16 nDepth);

/*
 * 主位图更新函数 - 资源分配/释放的核心操作
 * 
 * @param nChn: 通道号
 * @param bUse: TRUE=分配资源(将1置0), FALSE=释放资源(将0置1)
 * @param dwMask: 资源掩码，指定要操作的位置
 * @param nIndex: 起始行索引
 * @param nDepth: 操作的行数（高度）
 * 
 * 关键设计：
 * - bUse=TRUE时：与掩码取反进行AND操作，将指定位置置0（占用）
 * - bUse=FALSE时：直接OR操作，将指定位置置1（释放）
 */
void NvrSgwResBitMapUpdate(s32 nChn, BOOL bUse, u32 dwMask, s32 nIndex, s32 nDepth)
{
    s32 i = 0;

    // 边界检查：确保操作不会越界访问位图数组
    if ((nIndex + nDepth) > RES_BITMAP_MAX_NUM)
    {
        SGWPRINTERR("depth large  err ! %2d %2d %2d \n", nIndex, nDepth, RES_BITMAP_MAX_NUM);
        return;
    }

    // 调试信息：记录位图操作的详细参数
    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nChn:%d bUse:%d nIndex:%d nDepth:%d mask:%x\n", nChn, bUse, nIndex, nDepth, dwMask);
    }

    // 核心位图操作：对指定范围内的每一行进行掩码操作
    for (i = nIndex; i < (nIndex + nDepth); i++)
    {
        if (bUse)  // 分配资源：将指定位置从可用(1)变为占用(0)
        {
            // 关键操作：与掩码取反进行AND，将掩码对应位置置0
            // 例如：原值0xFFFFFFFF，掩码0x0000000F，结果0xFFFFFFF0
            g_adwResMap[nChn][i] = g_adwResMap[nChn][i] & (~dwMask);
        }
        else       // 释放资源：将指定位置从占用(0)变为可用(1)
        {
            // 直接OR操作：将掩码对应位置置1
            // 例如：原值0xFFFFFFF0，掩码0x0000000F，结果0xFFFFFFFF
            g_adwResMap[nChn][i] = g_adwResMap[nChn][i] | dwMask;
        }
    }
}

/*
 * 音频解码器ID位图更新函数
 * 
 * @param bUse: TRUE=分配音频解码器, FALSE=释放音频解码器
 * @param nIndex: 解码器ID（1-based，内部转换为0-based）
 * 
 * 设计说明：
 * - 使用32位掩码管理最多32个音频解码器
 * - ID从1开始，内部转换为0-based索引进行位操作
 * - 1表示可用，0表示已分配（与主位图相同的逻辑）
 */
void NvrSgwAudDecIdBitMapUpdate(BOOL bUse, s32 nIndex)
{
    if (nIndex > 0)  // 只处理有效的解码器ID（>0）
    {
        nIndex -= 1;  // 转换为0-based索引：ID1对应bit0，ID2对应bit1...

        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("bUse:%d nIndex:%d \n", bUse, nIndex);
        }
        
        if (bUse)  // 分配解码器：将对应位置0（已占用）
        {
            // 与掩码取反进行AND操作，将指定位清0
            g_dwAudDecIdMap = g_dwAudDecIdMap & (~(0x1 << nIndex));
        }
        else       // 释放解码器：将对应位置1（可用）
        {
            // OR操作，将指定位设置为1
            g_dwAudDecIdMap = g_dwAudDecIdMap | (0x1 << nIndex);
        }
    }
}

/*
 * 视频解码器ID位图更新函数
 * 
 * @param nChn: 通道号（用于确定操作哪个芯片的解码器）
 * @param bUse: TRUE=分配解码器, FALSE=释放解码器
 * @param nIndex: 解码器ID（1-based）
 * 
 * 设计说明：
 * - 按芯片分组管理：通道0-1使用芯片0，通道2-3使用芯片1
 * - 每个芯片管理16个视频解码器（使用32位掩码的低16位）
 */
void NvrSgwVidDecIdBitMapUpdate(s32 nChn, BOOL bUse, s32 nIndex)
{
    if (nIndex > 0)  // 只处理有效的解码器ID
    {
        nIndex -= 1;  // 转换为0-based索引
        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("nChn:%d  bUse:%d nIndex:%d \n", nChn, bUse, nIndex);
        }
        
        if (bUse)  // 分配视频解码器
        {
            // 关键计算：nChn / NVR_SGW_MAX_CHIP_CHN_NUM 确定芯片索引
            // 通道0,1 -> 芯片0；通道2,3 -> 芯片1
            g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] = 
                g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] & (~(0x1 << nIndex));
        }
        else       // 释放视频解码器
        {
            g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] = 
                g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] | (0x1 << nIndex);
        }
    }
}

/*
 * 视频窗口ID位图更新函数
 * 
 * @param nChn: 通道号（每个通道独立管理窗口资源）
 * @param bUse: TRUE=分配窗口, FALSE=释放窗口  
 * @param nIndex: 窗口ID（1-based）
 * 
 * 设计说明：
 * - 每个通道独立管理最多16个显示窗口
 * - 使用16位掩码进行管理（u16类型）
 */
void NvrSgwVidWinIdBitMapUpdate(s32 nChn, BOOL bUse, s32 nIndex)
{
    if (nIndex > 0)  // 只处理有效的窗口ID
    {
        nIndex -= 1;  // 转换为0-based索引
        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("nChn:%d bUse:%d nIndex:%d \n", nChn, bUse, nIndex);
        }
        
        if (bUse)  // 分配窗口：将对应位清0
        {
            g_adwVidDecWinMap[nChn] = g_adwVidDecWinMap[nChn] & (~(0x1 << nIndex));
        }
        else       // 释放窗口：将对应位置1
        {
            g_adwVidDecWinMap[nChn] = g_adwVidDecWinMap[nChn] | (0x1 << nIndex);
        }
    }
}

/*
 * 从位图中获取可用的视频解码窗口ID
 * 
 * @param nChn: 通道号
 * @return: 可用的窗口ID（1-based），0表示无可用窗口
 * 
 * 算法逻辑：
 * - 扫描16位窗口位图，查找第一个值为1的位（可用窗口）
 * - 返回1-based的窗口ID给上层使用
 */
s32 NvrSgwGetVidDecWinFromMap(s32 nChn)
{
    s32 i = 0;
    s32 nDecWinId = 0;

    // 获取当前通道的窗口分配位图
    u32 dwBitMap = g_adwVidDecWinMap[nChn];
    
    // 扫描最多16个窗口位置，查找第一个可用窗口（bit=1）
    for (i = 0; i < NVR_SGW_MAX_CHN_VID_NUM; i++)
    {
        if (MASK_GET_BIT(dwBitMap, i))  // 检查第i位是否为1（可用）
        {
            nDecWinId = i + 1;  // 转换为1-based ID返回
            break;
        }
    }
    
    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nIndex:%d \n", nDecWinId);
    }
    return nDecWinId;
}

/*
 * 从位图中获取可用的视频解码器ID
 * 
 * @param nChn: 通道号（用于确定查找哪个芯片的解码器）
 * @return: 可用的解码器ID（1-based），0表示无可用解码器
 * 
 * 算法逻辑：
 * - 根据通道号确定芯片：通道0,1使用芯片0；通道2,3使用芯片1
 * - 每个芯片管理8个视频解码器（16个解码器/2个芯片）
 * - 查找第一个可用的解码器位
 */
s32 NvrSgwGetVidDecFromMap(s32 nChn)
{
    s32 i = 0;
    s32 nVidDecId = 0;
    // 获取对应芯片的视频解码器分配位图
    u32 dwBitMap = g_adwVidDecIdMap[nChn/NVR_SGW_MAX_CHIP_CHN_NUM];

    // 扫描每个芯片的解码器数量：总数/芯片数
    for (i = 0; i < NVR_SGW_MAX_VID_NUM / NVR_SGW_MAX_CHIP_CHN_NUM; i++)
    {
        if (MASK_GET_BIT(dwBitMap, i))  // 检查第i位是否为1（可用）
        {
            nVidDecId = i + 1;  // 转换为1-based ID返回
            break;
        }
    }
    
    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nIndex:%d \n", nVidDecId);
    }
    return nVidDecId;
}

/*
 * 从位图中获取可用的音频解码器ID - 复杂的分配策略
 * 
 * @param nChn: 通道号
 * @return: 可用的音频解码器ID（1-based），0表示无可用解码器
 * 
 * 关键设计：
 * - 支持两种编译模式：_use_aud_chn_8_定义与否
 * - 优先分配策略：前4个通道(0-3)优先使用对应范围的解码器
 * - 后备分配策略：其他通道或无可用时，从全局范围分配
 */
s32 NvrSgwGetAudDecFromMap(s32 nChn)
{
    s32 i = 0;
    s32 nAudDecId = 0;
    
// 条件编译：支持不同的音频解码器分配策略
#ifdef _use_aud_chn_8_
    s32 nRange = nChn;                          // 直接使用通道号作为范围
    s32 nDiff = NVR_SGW_MAX_CHN_AUD_NUM;        // 每个范围的解码器数量
#else
    s32 nRange = nChn / NVR_SGW_MAX_CHIP_CHN_NUM;  // 芯片级别的范围划分
    s32 nDiff = NVR_SGW_MAX_CHN_AUD_NUM * NVR_SGW_MAX_CHIP_CHN_NUM;  // 每个芯片的解码器数量
#endif

    // 优先分配策略：前4个通道(0-3)使用专用的解码器范围
    if (nChn >= 0 && nChn < 4)
    {
        // 在指定范围内查找可用解码器
        // 范围计算：nRange * nDiff 到 (nRange + 1) * nDiff - 1
        for (i = nRange * nDiff; i < (nRange + 1) * nDiff; i++)
        {
            if (MASK_GET_BIT(g_dwAudDecIdMap, i))  // 检查第i位是否可用
            {
                nAudDecId = i + 1;  // 转换为1-based ID
                break;
            }
        }
    }
    else  // 后备分配策略：其他通道或专用范围无可用解码器时
    {
        // 从全局音频解码器池中分配
        for (i = 0; i < NVR_SGW_MAX_AUD_NUM; i++)
        {
            if (MASK_GET_BIT(g_dwAudDecIdMap, i))  // 检查第i位是否可用
            {
                nAudDecId = i + 1;  // 转换为1-based ID
                break;
            }
        }
    }
    
    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nIndex:%d \n", nAudDecId);
    }
    return nAudDecId;
}

/*
 * 检查指定音频解码器ID是否可用
 * 
 * @param nDecId: 要检查的解码器ID（0-based）
 * @return: 可用则返回1-based ID，否则返回0
 * 
 * 用途：在分配前预检查特定解码器是否可用
 */
s32 NvrSgwAudDecMapCheck(s32 nDecId)
{
    s32 nAudDecId = 0;

    // 检查指定位置的解码器是否可用（bit=1表示可用）
    if (MASK_GET_BIT(g_dwAudDecIdMap, nDecId))
    {
        nAudDecId = nDecId + 1;  // 转换为1-based ID返回
    }

    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("check nIndex:%d return %d\n", nDecId, nAudDecId);
    }
    return nAudDecId;
}

/*
 * 资源高度验证函数 - 2D资源分配算法的垂直验证组件
 * 
 * @param ptPosInfo: 资源请求信息（包含尺寸要求）
 * @param nHIndex: 起始行索引（垂直位置）
 * @param ptFreeInfo: 候选资源信息（水平扫描的结果）
 * @param nArrIndex: 候选资源在数组中的索引
 * @return: 0=验证成功，-1=验证失败
 * 
 * 核心算法：
 * 1. 验证垂直方向上连续nDepth行都满足资源要求
 * 2. 检查主位图掩码匹配
 * 3. 验证特殊像素级资源的边界条件
 * 
 * 关键设计：
 * - 主位图验证：确保所有行的掩码位都可用
 * - 边界像素验证：检查起始和结束边界的剩余像素是否足够
 */
s32 NvrSgwGetResMapHeightCheck(TNvrSgwResPosInfo *ptPosInfo, s32 nHIndex, TNvrSgwResAvailableInfo *ptFreeInfo, s32 nArrIndex)
{
    u32 dwBitMapTmp = 0;
    s32 i = 0;
    s32 nRet = 0;
    s32 nBlen = 0, nElen = 0, nBpos = 0, nEpos = 0, nFreelen = 0;
    s32 nDepth = ptPosInfo->h;      // 需要的高度（行数）
    u32 dwMask = 0;
    s32 nMaskW = 0;

    // 边界检查：确保请求的高度不会越界
    if ((nHIndex + nDepth) > RES_BITMAP_MAX_NUM)
    {
        SGWPRINTDBG("depth large  err ! %2d %2d %2d \n", nHIndex, nDepth, RES_BITMAP_MAX_NUM);
        nRet = -1;
        return nRet;
    }
    
    // 从候选资源信息中提取关键参数
    nBpos = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS] - 1;     // 起始位置（转0-based）
    nEpos = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS] +        // 结束位置
            ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_LEN];
    nBlen = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_BEGIN];       // 起始边界像素长度
    nElen = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_END];         // 结束边界像素长度

    nMaskW = ptPosInfo->w;  // 基础宽度需求

    SGWPRINTDBG("b:%d e:%d bp:%d ep:%d wa:%d (w:%d %d %d)\n", nBlen, nElen, nBpos, nEpos, ptPosInfo->byWadd,
            nMaskW, nMaskW * RES_WIDTH_UNIT, ptPosInfo->ow);
            
    // 智能宽度调整：如果有额外像素需求且边界像素不足，增加一个资源单位
    if(ptPosInfo->byWadd)  // 存在额外像素需求
    {
        // 检查边界像素总和是否满足额外需求
        if ((nMaskW * RES_WIDTH_UNIT + nBlen + nElen) < ptPosInfo->ow)
        {
            nMaskW += 1;  // 增加一个资源单位来满足像素需求
        }
    }

    // 生成用于验证的掩码
    dwMask = CHECK_MASK(nMaskW, ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS]);

    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("check mask 0x%x -----> 0x%x (offset:%d)\n", UNIT_LEN_MASK(nMaskW), dwMask,
                ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS]);
    }

    // 核心验证循环：检查垂直方向上每一行都满足要求
    for (i = nHIndex; i < nHIndex + nDepth; i++)
    {
        // 获取当前行的位图状态
        dwBitMapTmp = g_adwResMap[ptPosInfo->nChn][i];

        // 主位图验证：检查掩码对应位置是否都可用
        // (dwBitMapTmp & dwMask) < dwMask 表示有些位不可用
        if ((dwBitMapTmp & dwMask) < dwMask)
        {
            SGWPRINTDBG("index %2d check err ! %2d %2d 0x%x 0x%x \n", i, nHIndex, nDepth, dwMask, dwBitMapTmp);
            nRet = -1;
            break;
        }

        // 起始边界像素验证：检查起始位置的剩余像素是否足够
        if (nBlen)
        {
            // 计算起始位置的可用像素数
            nFreelen = RES_WIDTH_UNIT - ((g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nEpos] >> 8) & 0xff);
            if (nFreelen < nBlen)
            {
                SGWPRINTDBG("index %2d check nBlen err ! %2d %2d 0x%x 0x%x \n", i, nHIndex, nDepth,
                        nBlen, g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nBpos]);
                nRet = -1;
                break;
            }
        }

        // 结束边界像素验证：检查结束位置的剩余像素是否足够
        if (nElen)
        {
            // 计算结束位置的可用像素数
            nFreelen = RES_WIDTH_UNIT - (g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nBpos] & 0xff);
            if ( nFreelen < nElen)
            {
                SGWPRINTDBG("index %2d check nElen err ! %2d %2d 0x%x 0x%x \n", i, nHIndex, nDepth,
                        nElen, g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nEpos]);
                nRet = -1;
                break;
            }
        }
    }

    return nRet;  // 0=成功，-1=失败
}


/*
 * 候选资源信息更新函数 - 有序插入新发现的可用资源
 * 
 * @param nPos: 资源起始位置
 * @param nCount: 连续可用单位数量
 * @param nBLen: 起始边界额外像素长度
 * @param nELen: 结束边界额外像素长度
 * @param ptInfo: 候选资源信息结构（输出）
 * 
 * 关键算法：
 * - 按资源大小排序插入（小的在前，大的在后）
 * - 使用总像素数作为排序依据：nCount * RES_WIDTH_UNIT + nBLen + nELen
 * - 保持已有数组的有序性，使用插入排序的思想
 */
void NvrSgwBitMapInfoUpdate(s32 nPos, s32 nCount, s32 nBLen, s32 nELen, TNvrSgwResAvailableInfo *ptInfo)
{
    s32 i = 0;
    u8 abyBitMapInfo[RES_BITMAP_LEN][RES_BITMAP_INFO_NUM];
    s32 nFree = 0, nArrayFree = 0;

    // 临时备份现有数据，用于后续的数组移动操作
    mzero(abyBitMapInfo);

    if (ptInfo->nArrayCount)  // 如果已有候选资源
    {
        // 备份现有的候选资源信息
        memcpy(abyBitMapInfo, ptInfo->abyBitMapInfo, sizeof(abyBitMapInfo[0]) * ptInfo->nArrayCount);

        // 计算新资源的总像素数（用于排序比较）
        nFree = nCount * RES_WIDTH_UNIT + nBLen + nELen;
        
        // 查找合适的插入位置：按资源大小升序排列
        for (i = 0; i < ptInfo->nArrayCount; i++ )
        {
            // 计算现有资源的总像素数
            nArrayFree = ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_LEN] * RES_WIDTH_UNIT
                    + ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_BEGIN] + ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_END];
                    
            // 如果新资源小于等于当前资源，找到插入位置
            if (nFree <= nArrayFree)
            {
                if (g_nBitmapPrint > 1)
                {
                    SGWPRINTDBG("find pos %d , count:%d\n", i, ptInfo->nArrayCount);
                }
                break;
            }
        }
    }

    // 在找到的位置插入新的资源信息
    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_LEN] = nCount;    // 连续单位数
    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_POS] = nPos;      // 起始位置
    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_BEGIN] = nBLen;   // 起始边界像素
    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_END] = nELen;     // 结束边界像素

    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("add index:%d, pos %d, len:%d b:%d e:%d\n", i, nPos, nCount, nBLen, nELen);
    }

    // 如果不是插入到末尾，需要移动后续元素为新元素腾出空间
    if (i != ptInfo->nArrayCount)
    {
        // 将原位置i及之后的元素向后移动一位
        memcpy(ptInfo->abyBitMapInfo[i + 1], abyBitMapInfo[i], sizeof(abyBitMapInfo[0]) * (ptInfo->nArrayCount - i));
    }

    ptInfo->nArrayCount += 1;  // 候选资源总数加1
}

/*
 * 资源宽度扫描函数 - 2D资源分配算法的水平扫描组件
 * 
 * @param ptPosInfo: 资源请求信息
 * @param nHIndex: 当前扫描的行索引
 * @param ptFreeInfo: 输出的候选资源信息
 * @return: 0 (始终返回0，实际结果通过ptFreeInfo返回)
 * 
 * 核心算法：位图水平扫描 + 特殊像素处理
 * 1. 快速掩码预检：dwBitMap >= dwMask
 * 2. 逐位扫描：从右到左（MSB到LSB）检查每个位
 * 3. 状态机处理：跟踪连续可用区域的开始和结束
 * 4. 边界像素计算：处理不规整分辨率的剩余像素
 * 5. 候选资源收集：将满足条件的资源区域加入候选列表
 */
s32 NvrSgwGetResMapWidthCheck(TNvrSgwResPosInfo *ptPosInfo, s32 nHIndex, TNvrSgwResAvailableInfo *ptFreeInfo)
{
    u32 dwBitMap = 0, dwMask = 0;
    s32 i = 0;
    s32 nFreeLen = 0, nCount = 0, nBLen = 0, nELen = 0;
    BOOL bZero = FALSE, bFFree = TRUE;  // 状态标志：是否遇到0位，是否在空闲区域
    s32 nPos = 0;
    u32 dwSpecMap = 0;

    // 获取当前行的位图状态
    dwBitMap = g_adwResMap[ptPosInfo->nChn][nHIndex];
    dwMask = UNIT_LEN_MASK(ptPosInfo->w);  // 生成基础需求掩码

    // 快速预检：位图值必须大于等于所需掩码才可能有足够连续位
    // 这是一个优化：如果位图中连续1的数量不足，直接跳过详细扫描
    if (dwBitMap >= dwMask)
    {
        // 核心扫描循环：从右到左（bit 31到bit 0）扫描每个位
        // 注意：使用从1到32的循环，内部转换为30到0的位索引
        for (i = 1; i <= RES_BITMAP_LEN; i++)
        {
            nPos = i - 1;  // 转换为0-based位索引
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("bit:%d pos:%d zero:%d bFFree:%d\n", MASK_GET_BIT(dwBitMap, RES_BITMAP_LEN - i), nPos, bZero, bFFree);
            }
            
            // 检查当前位是否为1（可用）
            if (MASK_GET_BIT(dwBitMap, RES_BITMAP_LEN - i))
            {
                // 当前位可用的处理逻辑
                if (bZero)  // 如果之前遇到了0位，现在重新开始计数
                {
                    // 检查前一个不可用位置是否有剩余像素可利用
                    dwSpecMap = g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nPos - 1];
                    if (g_nBitmapPrint > 4)
                    {
                        SGWPRINTDBG("get pos %d, 0x%x\n", nPos, dwSpecMap);
                    }
                    
                    // 边界像素处理：如果需要额外像素且前一位置有可用剩余像素
                    if (dwSpecMap && ptPosInfo->byWadd)
                    {
                        // 检查前一位置的结束边界是否完全可用
                        if (0 == (dwSpecMap & 0xff))  // 低8位为0表示没有起始占用
                        {
                            // 计算可用的起始边界像素数
                            nBLen = RES_WIDTH_UNIT - ((dwSpecMap >> 8) & 0xff);
                        }
                    }
                    else
                    {
                        nBLen = 0;  // 无额外起始像素
                    }
                }

                nCount++;           // 连续可用单位数加1
                bFFree = TRUE;      // 标记当前在空闲区域
                bZero = FALSE;      // 重置0位标志
            }
            else
            {
                // 当前位不可用（为0）的处理逻辑
                bZero = TRUE;  // 标记遇到了0位
                
                // 如果之前在空闲区域，需要检查当前位置是否有剩余像素可利用
                if (bFFree)
                {
                    dwSpecMap = g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nPos];
                    if (g_nBitmapPrint > 4)
                    {
                        SGWPRINTDBG("zero pos %d, 0x%x\n", nPos, dwSpecMap);
                    }
                    
                    // 检查当前不可用位置是否有剩余像素
                    if (dwSpecMap && ptPosInfo->byWadd)
                    {
                        // 检查高8位是否为0（没有结束占用）
                        if (0 == ((dwSpecMap >> 8) & 0xff))
                        {
                            // 计算可用的结束边界像素数
                            nELen = RES_WIDTH_UNIT - (dwSpecMap & 0xff);
                        }
                    }
                    else
                    {
                        nELen = 0;  // 无额外结束像素
                    }

                    bFFree = FALSE;  // 标记离开了空闲区域
                }
            }
            
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("i:%2d, count:%2d, bZero:%d nBLen:%2d nELen:%d\n", i, nCount, bZero, nBLen, nELen);
            }
            
            // 连续区域结束判断：遇到0位或扫描到最后一位
            if (bZero || i == (RES_BITMAP_LEN))
            {
                // 计算连续区域的起始位置
                nPos = bZero ? i - 1 - nCount : RES_BITMAP_LEN - nCount;

                // 计算总的可用像素数：主单位像素 + 边界额外像素
                nFreeLen = nCount * RES_WIDTH_UNIT + nBLen + nELen;
                
                // 检查是否满足原始宽度需求
                if (nFreeLen >= ptPosInfo->ow)
                {
                    // 将满足条件的候选资源添加到列表中（按大小有序插入）
                    NvrSgwBitMapInfoUpdate(nPos, nCount, nBLen, nELen, ptFreeInfo);
                    if (g_nBitmapPrint > 1)
                    {
                        SGWPRINTDBG("index:%2d, i:%2d, count:%2d, pos:%2d--%2d, b:%d, e:%d is empty (%d)\n",
                            nHIndex, i, nCount, nPos, nPos + nCount - 1, nBLen, nELen, nFreeLen);
                    }
                }

                nCount = 0;  // 重置计数器，准备下一个连续区域的扫描
            }
        }
    }
    else
    {
        // 快速预检失败，当前行不满足基本要求
        // 注释掉的调试信息：SGWPRINTINFO("bitmap %d not empty \n", nHIndex);
    }

    return 0;  // 始终返回0，实际结果通过ptFreeInfo参数返回
}


/*
 * 检查两个矩形资源是否重叠 - 资源冲突检测
 * 
 * @param rect1: 第一个矩形资源
 * @param rect2: 第二个矩形资源  
 * @return: 1=重叠或越界, 0=不重叠且在范围内
 * 
 * 算法说明：
 * 1. 将资源单位坐标转换为实际像素坐标
 * 2. 使用经典的矩形重叠检测算法
 * 3. 额外检查是否超出系统最大分辨率限制
 * 
 * 坐标转换逻辑：
 * - x坐标 = x * RES_WIDTH_UNIT - byBadd（减去起始边界偏移）
 * - y坐标 = y * RES_HEIGHT_UNIT（直接转换）
 * - 宽度使用原始像素宽度ow，高度使用单位高度转换
 */
int do_rectangles_overlap(TNvrSgwResPosInfo rect1, TNvrSgwResPosInfo rect2)
{
    // 计算两个矩形的实际像素坐标
    int x1 = rect1.x * RES_WIDTH_UNIT - rect1.byBadd;    // 矩形1左边界
    int x2 = rect2.x * RES_WIDTH_UNIT - rect2.byBadd;    // 矩形2左边界
    int y1 = rect1.y * RES_HEIGHT_UNIT;                  // 矩形1上边界
    int y2 = rect2.y * RES_HEIGHT_UNIT;                  // 矩形2上边界
    int h1 = rect1.h * RES_HEIGHT_UNIT;                  // 矩形1高度（像素）
    int h2 = rect2.h * RES_HEIGHT_UNIT;                  // 矩形2高度（像素）

    // 经典矩形重叠检测：如果满足任一分离条件则不重叠
    if ((x1 + rect1.ow) <= x2 ||      // rect1 在 rect2 左侧
           x1 >= (x2 + rect2.ow) ||   // rect1 在 rect2 右侧  
            (y1 + h1) <= y2 ||        // rect1 在 rect2 上侧
            y1 >= (y2 + h2))          // rect1 在 rect2 下侧
    {
        return 0;  // 不重叠
    }

    // 调试输出：打印重叠的矩形信息
    OspPrintf(TRUE, FALSE, "x:%-4d, y:%-4d, w:%-4d, h:%-4d \t", x1, y1, rect1.ow, h1);
    OspPrintf(TRUE, FALSE, "x:%-4d, y:%-4d, w:%-4d, h:%-4d \n", x2, y2, rect2.ow, h2);

    // 系统边界检查：检查是否超出最大分辨率限制（4K: 3840x2160）
    if ( (x1 + rect1.ow)  > 3840|| (x2 + rect2.ow)  > 3840
            || (y1 + h1) > 2160 || (y2 + h2) > 2160)
    {
        return 1;  // 超出系统分辨率限制
    }

    return 1;  // 存在重叠
}

/*
 * 检查资源数组中所有矩形是否存在重叠 - 批量冲突检测
 * 
 * @param rects: 矩形资源数组
 * @param n: 矩形数量
 * @return: 1=存在重叠, 0=无重叠
 * 
 * 算法：O(n²)的两两比较算法
 * - 对于数组中的每一对矩形，调用do_rectangles_overlap进行检测
 * - 一旦发现重叠立即返回，避免不必要的后续比较
 */
int check_all_rectangles(TNvrSgwResPosInfo rects[], int n)
{
    // 双重循环：检查每一对矩形的重叠情况
    for (int i = 0; i < n; i++)
    {
        // 只需检查i之后的矩形，避免重复比较（i,j)和(j,i)
        for (int j = i + 1; j < n; j++)
        {
            // 调用单个矩形重叠检测函数
            if (do_rectangles_overlap(rects[i], rects[j]))
            {
                SGWPRINTERR("check_all_rectangles true i = %d  j = %d\n", i , j);
                return 1;  // 发现重叠，立即返回
            }
        }
    }
    return 0;  // 所有矩形都不重叠
}

/*
 * 检查指定通道的所有已分配资源是否存在冲突
 * 
 * @param nChn: 通道号
 * @return: 1=存在冲突, 0=无冲突
 * 
 * 功能：对指定通道当前所有已分配的资源进行全面的冲突检测
 */
s32 NvrSgwCheckAllRects(s32 nChn)
{
    // 检查当前通道所有已分配资源是否存在重叠
    if(check_all_rectangles(g_atResUseMap[nChn].atResPos, g_atResUseMap[nChn].nArrayCount))
    {
        SGWPRINTERR("check_all_rectangles true chn %d \n", nChn);
        return 1;  // 发现冲突
    }

    return 0;  // 无冲突
}

void NvrSgwShowResBitMap(s32 nChip)
{
    s32 i = 0, j = 0, k = 0;
    s32 nChn = nChip * 2;

    nChn = nChn > 2 ? 2 : nChn;

    OspPrintf(TRUE, FALSE, "%d   0                  10                  20                  30\n", nChn);
    for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
    {
        OspPrintf(TRUE, FALSE, "%2d: ", i);
        for (j = 0; j < RES_BITMAP_LEN; j++)
        {
            OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[nChn][i], RES_BITMAP_LEN - 1 - j));
        }

        OspPrintf(TRUE, FALSE, "\n");
    }
    OspPrintf(TRUE, FALSE, "\n");
    for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
    {
        OspPrintf(TRUE, FALSE, "%2d: ", i);
        for (j = 0; j < RES_BITMAP_LEN; j++)
        {
            OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[nChn + 1][i], RES_BITMAP_LEN - 1 - j));
        }

        OspPrintf(TRUE, FALSE, "\n");
    }

    OspPrintf(TRUE, FALSE, "use list   x   y   w   h  \n");

    k = nChn + 2;
    for (; nChn < k; nChn++ )
    {
        for (i = 0; i < g_atResUseMap[nChn].nArrayCount; i++)
        {
            OspPrintf(TRUE, FALSE, "index:%2d %3d %3d %3d %3d  %-3u [%d] vid:%2d aud:%2d win:%2d wa:%2d b:%2d e:%2d\n",  i,
                    g_atResUseMap[nChn].atResPos[i].x,
                    g_atResUseMap[nChn].atResPos[i].y,
                    g_atResUseMap[nChn].atResPos[i].w,
                    g_atResUseMap[nChn].atResPos[i].h,
                    g_atResUseMap[nChn].atResPos[i].nUniq,
                    g_atResUseMap[nChn].atResPos[i].nChn,
                    g_atResUseMap[nChn].atResPos[i].nVidDecId,
                    g_atResUseMap[nChn].atResPos[i].nAudDecId,
                    g_atResUseMap[nChn].atResPos[i].nChnWinIndex,
                    g_atResUseMap[nChn].atResPos[i].byWadd,
                    g_atResUseMap[nChn].atResPos[i].byBadd,
                    g_atResUseMap[nChn].atResPos[i].byEadd);
        }
        OspPrintf(TRUE, FALSE, "\n");
        OspPrintf(TRUE, FALSE, "total %d \n", g_atResUseMap[nChn].nArrayCount);
        if(check_all_rectangles(g_atResUseMap[nChn].atResPos, g_atResUseMap[nChn].nArrayCount))
        {
            SGWPRINTERR("check_all_rectangles true chn %d\n", nChn);
        }
    }
}

void NvrSgwShowChnResBitMap(s32 nChn)
{
    s32 i = 0, j = 0;

    OspPrintf(TRUE, FALSE, "%d   0                  10                  20                  30\n", nChn);
    for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
    {
        OspPrintf(TRUE, FALSE, "%2d: ", i);
        for (j = 0; j < RES_BITMAP_LEN; j++)
        {
            OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[nChn][i], RES_BITMAP_LEN - 1 - j));
        }

        OspPrintf(TRUE, FALSE, "\n");
    }
}

void NvrSgwShowAllStreamBitMap(s32 nChip)
{
    s32 i = 0;

    for (i = 0; i < g_atStreamUseInfo[nChip].nArrayCount; i++)
    {
        OspPrintf(TRUE, FALSE, "all list   x   y   w   h  \n");
        OspPrintf(TRUE, FALSE, "index:%2d %3d %3d %3d %3d  %-3u [%d] vid:%d aud:%d win:%d\n",  i,
                g_atStreamUseInfo[nChip].atResPos[i].x,
                g_atStreamUseInfo[nChip].atResPos[i].y,
                g_atStreamUseInfo[nChip].atResPos[i].w,
                g_atStreamUseInfo[nChip].atResPos[i].h,
                g_atStreamUseInfo[nChip].atResPos[i].nUniq,
                g_atStreamUseInfo[nChip].atResPos[i].nChn,
                g_atStreamUseInfo[nChip].atResPos[i].nVidDecId,
                g_atStreamUseInfo[nChip].atResPos[i].nAudDecId,
                g_atStreamUseInfo[nChip].atResPos[i].nChnWinIndex);
    }

}

s32 NvrSgwStreamChipUseMapUpdate(TNvrSgwResPosInfo *ptPosInfo)
{
    TNvrSgwResAllUseInfo *ptStreamInfo = NULL;
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_VID_NUM + NVR_SGW_MAX_AUD_NUM];
    s32 i = 0;
    s32  w = 0,  h = 0;

    ptStreamInfo = &g_atStreamUseInfo[ptPosInfo->nChn / 2];

    w = ptPosInfo->w;
    h = ptPosInfo->h;

    if (ptStreamInfo->nArrayCount)
    {
        memcpy(atResPos, ptStreamInfo->atResPos, sizeof(atResPos[0]) * ptStreamInfo->nArrayCount);

        for (i = 0; i < ptStreamInfo->nArrayCount; i++ )
        {
            if (w <= ptStreamInfo->atResPos[i].w)
            {
                if (h <= ptStreamInfo->atResPos[i].h)
                {
                    ///< wh<= find pos
                    SGWPRINTFREQ("wh<= find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                    break;
                }
                else
                {
                    ///< w<=  h> find next pos
                    if (w < ptStreamInfo->atResPos[i].w)
                    {
                        SGWPRINTFREQ("w< find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                        break;
                    }

                    if (i < (ptStreamInfo->nArrayCount - 1))
                    {
                        if (ptStreamInfo->atResPos[i].w < ptStreamInfo->atResPos[i + 1].w)
                        {
                            if (w == ptStreamInfo->atResPos[i].w)
                            {
                                i++;
                            }
                            SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                            break;
                        }
                    }
                    else if (i == (ptStreamInfo->nArrayCount - 1))
                    {
                        i++;
                        SGWPRINTFREQ("last find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                        break;
                    }
                    else
                    {
                        SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                        break;
                    }
                }

            }
        }
    }

    memcpy(&ptStreamInfo->atResPos[i], ptPosInfo, sizeof(ptStreamInfo->atResPos[0]));

    if (i != ptStreamInfo->nArrayCount)
    {
        memcpy(&ptStreamInfo->atResPos[i + 1], &atResPos[i], sizeof(atResPos[0]) * (ptStreamInfo->nArrayCount - i));
    }

    ptStreamInfo->nArrayCount += 1;

    return ptStreamInfo->nArrayCount;
}


void NvrSgwSpecMapArrUpdate(TNvrSgwResPosInfo *ptPosInfo, u8 byOpt)
{
    s32 i = 0;
    s32 x = 0, y = 0, w = 0,  h = 0, nChn = 0;
    nChn = ptPosInfo->nChn;
    x = ptPosInfo->x;
    y = ptPosInfo->y;
    w = ptPosInfo->w;
    h = ptPosInfo->h;

    for (i = y; i < (y + h); i++)
    {
        if (ptPosInfo->byBadd && x > 0)
        {
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig 0x%x, %d %d 0x%x(%d)\n", g_adwResMapSpe[nChn][i][x - 1], i, x - 1, ptPosInfo->byBadd, ptPosInfo->byBadd);
            }
            if (1 == byOpt)
            {
                g_adwResMapSpe[nChn][i][x - 1] |=  ptPosInfo->byBadd;
            }
            else
            {
                g_adwResMapSpe[nChn][i][x - 1] &=  (~0xff);
                if (0 == g_adwResMapSpe[nChn][i][x - 1])
                {
                    NvrSgwFreeResToBitMap(nChn, x - 1, i, 1, 1);
                }
            }
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig byBadd 0x%x\n", g_adwResMapSpe[nChn][i][x - 1]);
            }
        }
        if (ptPosInfo->byEadd && (x + w) < 32)
        {
            u32 dwTmp = ptPosInfo->byEadd;
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig 0x%x, %d %d 0x%x(%d)\n", g_adwResMapSpe[nChn][i][x + w], i, x + w, ptPosInfo->byEadd, ptPosInfo->byEadd);
            }
            if (1 == byOpt)
            {
                g_adwResMapSpe[nChn][i][x + w] |=  ((dwTmp << 8) & (~0xff));
            }
            else
            {
                g_adwResMapSpe[nChn][i][x + w] &=  (~0xff00);
                if (0 == g_adwResMapSpe[nChn][i][x + w])
                {
                    NvrSgwFreeResToBitMap(nChn, x + w, i, 1, 1);
                }
            }
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig byEadd %x\n", g_adwResMapSpe[nChn][i][x + w]);
            }
        }
    }
}

u32 NvrSgwStreamUseMapUpdate(TNvrSgwResUseInfo *ptResUseInfo, TNvrSgwResPosInfo *ptPosInfo)
{
    static s32 s_nUniq = 1;
    s32 i = 0;
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_CHN_VID_NUM];
    s32 x = 0, y = 0, w = 0,  h = 0, nChn = 0;
    u32 dwMask = 0;
    s32 nMaskW = 0;

    if (!ptPosInfo || !ptResUseInfo)
    {
        return 0;
    }
    nChn = ptPosInfo->nChn;
    x = ptPosInfo->x;
    y = ptPosInfo->y;
    w = ptPosInfo->w;
    h = ptPosInfo->h;

    dwMask = UNIT_LEN_MASK(w);

    if (ptResUseInfo->nArrayCount)
    {
        memcpy(atResPos, ptResUseInfo->atResPos, sizeof(atResPos[0]) * ptResUseInfo->nArrayCount);

        for (i = 0; i < ptResUseInfo->nArrayCount; i++ )
        {
            if (w <= ptResUseInfo->atResPos[i].w)
            {
                if (h <= ptResUseInfo->atResPos[i].h)
                {
                    ///< wh<= find pos
                    SGWPRINTFREQ("wh<= find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                    break;
                }
                else
                {
                    ///< w<=  h> find next pos
                    if (w < ptResUseInfo->atResPos[i].w)
                    {
                        SGWPRINTFREQ("w< find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                        break;
                    }

                    if (i < (ptResUseInfo->nArrayCount - 1))
                    {
                        if (ptResUseInfo->atResPos[i].w < ptResUseInfo->atResPos[i + 1].w)
                        {
                            if (w == ptResUseInfo->atResPos[i].w)
                            {
                                i++;
                            }
                            SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                            break;
                        }
                    }
                    else if (i == (ptResUseInfo->nArrayCount - 1))
                    {
                        i++;
                        SGWPRINTFREQ("last find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                        break;
                    }
                    else
                    {
                        SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                        break;
                    }
                }

            }
        }
    }

    ptResUseInfo->atResPos[i].x = x;
    ptResUseInfo->atResPos[i].y = y;
    ptResUseInfo->atResPos[i].w = w;
    ptResUseInfo->atResPos[i].h = h;
    ptResUseInfo->atResPos[i].ow = ptPosInfo->ow;
    ptResUseInfo->atResPos[i].byWadd = ptPosInfo->byWadd;
    ptResUseInfo->atResPos[i].byBadd = ptPosInfo->byBadd;
    ptResUseInfo->atResPos[i].byEadd = ptPosInfo->byEadd;
    ptResUseInfo->atResPos[i].nUniq = ptPosInfo->nUniq ? ptPosInfo->nUniq : s_nUniq;
    ptResUseInfo->atResPos[i].nChn = nChn;

    ///< 需要分配音频
    if (0 == ptPosInfo->nUniq && ptPosInfo->nAudDecId)
    {
        ptResUseInfo->nArrayAudCount += 1;
        NvrSgwAudDecIdBitMapUpdate(TRUE, ptPosInfo->nAudDecId);
    }
    ptResUseInfo->atResPos[i].nAudDecId = ptPosInfo->nAudDecId;

    if (w > 0 && h > 0)
    {
        ptResUseInfo->atResPos[i].nVidDecId = ptPosInfo->nVidDecId ? ptPosInfo->nVidDecId : NvrSgwGetVidDecFromMap(nChn);
        ptResUseInfo->atResPos[i].nChnWinIndex = ptPosInfo->nChnWinIndex ? ptPosInfo->nChnWinIndex : NvrSgwGetVidDecWinFromMap(nChn);

        SGWPRINTDBG("b:%d e:%d wa:%d\n", ptPosInfo->byBadd, ptPosInfo->byEadd,  ptPosInfo->byWadd);
        nMaskW = w;
        if (ptPosInfo->byEadd)
        {
            nMaskW = w + 1;
        }
        dwMask = CHECK_MASK(nMaskW, x);
        NvrSgwResBitMapUpdate(nChn, TRUE, dwMask, y, h);
        NvrSgwSpecMapArrUpdate(ptPosInfo, 1);

        if (ptResUseInfo->atResPos[i].nVidDecId > 0)
        {
            NvrSgwVidDecIdBitMapUpdate(nChn, TRUE, ptResUseInfo->atResPos[i].nVidDecId);
        }
        if (ptResUseInfo->atResPos[i].nChnWinIndex > 0)
        {
            NvrSgwVidWinIdBitMapUpdate(nChn, TRUE, ptResUseInfo->atResPos[i].nChnWinIndex);
        }


    }
    else
    {
        ptResUseInfo->atResPos[i].nVidDecId = 0;
        ptResUseInfo->atResPos[i].nChnWinIndex = 0;
    }

    NvrSgwStreamChipUseMapUpdate(&ptResUseInfo->atResPos[i]);

    s_nUniq = (s_nUniq + 1) % 0x7fffffff;

    if (i != ptResUseInfo->nArrayCount)
    {
        memcpy(&ptResUseInfo->atResPos[i + 1], &atResPos[i], sizeof(atResPos[0]) * (ptResUseInfo->nArrayCount - i));
    }

    ptResUseInfo->nArrayCount += 1;

    return ptResUseInfo->atResPos[i].nUniq;
}

/*
 * ==================== 核心资源分配主函数 ====================
 * 2D位图资源分配算法 - 水平扫描 + 垂直验证 + 最优匹配
 * 
 * @param ptPosInfo: 资源请求信息（输入/输出）
 * @return: 分配成功返回唯一ID，失败返回0
 * 
 * 算法核心思想："先水平扫描，再垂直验证"的二阶段分配策略
 * 
 * 第一阶段 - 水平扫描：
 * 1. 逐行扫描32x12位图的每一行
 * 2. 对每行调用NvrSgwGetResMapWidthCheck进行水平方向的可用区域检测
 * 3. 收集所有可能的候选资源区域（按大小排序存储）
 * 
 * 第二阶段 - 垂直验证：
 * 4. 对每个候选区域调用NvrSgwGetResMapHeightCheck验证垂直连续性
 * 5. 检查从起始行开始的连续nDepth行是否都满足资源需求
 * 6. 验证特殊像素级资源的边界条件
 * 
 * 第三阶段 - 资源分配：
 * 7. 找到第一个通过验证的候选区域
 * 8. 调用NvrSgwStreamUseMapUpdate进行实际的资源分配和位图更新
 * 9. 返回分配成功的资源唯一ID
 * 
 * 关键优化：
 * - 快速退出机制：一旦找到合适资源立即返回，不进行后续扫描
 * - 渐进式匹配：优先使用最小满足需求的资源，减少碎片化
 * - 边界像素处理：支持非整数单位的精确像素级分配
 */
u32 NvrSgwGetResFromBitMap(TNvrSgwResPosInfo *ptPosInfo)
{
    s32 i = 0, j = 0;
    u32 dwMask = 0;
    s32 nRet = 0;
    BOOL bFind = FALSE;                        // 资源查找成功标志
    TNvrSgwResAvailableInfo tBitMapInfo;       // 候选资源信息容器
    u32 dwUniq = 0;                            // 分配成功的资源唯一ID
    s32 nChn = 0, nUnitLen = 0, nDepth = 0;
    static s32 s_nCallCount = 0;               // 函数调用计数器（用于调试）

    // 初始化候选资源信息结构
    mzero(tBitMapInfo);
    s_nCallCount++;

    // 参数有效性检查
    if (!ptPosInfo)
    {
        SGWPRINTERR("param in err \n");
        return 0;
    }
    
    // 提取关键参数
    nChn = ptPosInfo->nChn;         // 目标通道
    nUnitLen = ptPosInfo->w;        // 需要的宽度单位数
    nDepth = ptPosInfo->h;          // 需要的高度单位数

    // 生成基础掩码用于快速预检
    dwMask = UNIT_LEN_MASK(nUnitLen);

    // 详细调试信息输出：记录函数调用的完整参数信息
    if (g_nBitmapPrint > 0)
    {
        SGWPRINTIMP(">>>>> [CALL-%d] NvrSgwGetResFromBitMap ENTER: ow=%d w=%d h=%d chn=%d vid=%d aud=%d uniq=%u wa=%d ba=%d ea=%d\n",
            s_nCallCount,
            ptPosInfo ? ptPosInfo->ow : -1,
            ptPosInfo ? ptPosInfo->w : -1, 
            ptPosInfo ? ptPosInfo->h : -1,
            ptPosInfo ? ptPosInfo->nChn : -1,
            ptPosInfo ? ptPosInfo->nVidDecId : -1,
            ptPosInfo ? ptPosInfo->nAudDecId : -1,
            ptPosInfo ? ptPosInfo->nUniq : 0,
            ptPosInfo ? ptPosInfo->byWadd : -1,
            ptPosInfo ? ptPosInfo->byBadd : -1,
            ptPosInfo ? ptPosInfo->byEadd : -1);
    }

    // 通道资源限制检查：防止单通道视频资源过载
    if ((g_atResUseMap[nChn].nArrayCount - g_atResUseMap[nChn].nArrayAudCount ) >= NVR_SGW_MAX_CHN_VID_NUM)
    {
        return 0;  // 当前通道视频资源已满
    }

    // 特殊情况处理：仅音频资源或无效尺寸请求
    if (0 == nUnitLen || 0 == nDepth)
    {
        // 直接调用流使用映射更新（通常用于纯音频资源分配）
        dwUniq = NvrSgwStreamUseMapUpdate(&g_atResUseMap[nChn], ptPosInfo);
        return dwUniq;
    }

    // ==================== 核心分配算法开始 ====================
    // 第一阶段：逐行水平扫描，寻找候选资源区域
    // 扫描范围：从第0行到第(RES_BITMAP_MAX_NUM - nDepth)行
    // 这样确保找到的起始位置能够容纳完整的nDepth高度
    for (i = 0; i < (RES_BITMAP_MAX_NUM - nDepth + 1); i++)
    {
        // 对当前行进行水平扫描，查找可用的连续资源区域
        nRet = NvrSgwGetResMapWidthCheck(ptPosInfo, i, &tBitMapInfo);
        
        // 如果当前行找到了候选资源区域
        if (tBitMapInfo.nArrayCount > 0)
        {
            // 第二阶段：对每个候选区域进行垂直验证
            // 遍历所有候选区域（已按大小排序，优先选择小的）
            for (j = 0; j < tBitMapInfo.nArrayCount; j++)
            {
                // 验证从当前行开始的连续nDepth行是否都满足要求
                nRet = NvrSgwGetResMapHeightCheck(ptPosInfo, i, &tBitMapInfo, j);
                if (0 == nRet)  // 验证成功
                {
                    bFind = TRUE;  // 标记找到合适资源
                    if (g_nBitmapPrint > 0)
                    {
                        SGWPRINTDBG("chn:%d find res succ! index:%d pos: (x:%2d, y:%2d, len:%2d, depth:%2d) is empty \n",
                            nChn, j, tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_POS], i, nUnitLen, nDepth);
                    }

                    // 第三阶段：资源分配或预分配
                    if (ptPosInfo->byPrepare)  // 仅做预分配测试，不实际占用
                    {
                        dwUniq = 1;  // 返回测试成功标志
                        SGWPRINTDBG("arrange only test not true apply\n");
                    }
                    else  // 执行实际的资源分配
                    {
                        // 设置分配结果的坐标信息
                        ptPosInfo->x = tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_POS];     // X坐标
                        ptPosInfo->y = i;                                                      // Y坐标
                        // 宽度取较小值：防止过度分配
                        ptPosInfo->w = ptPosInfo->w < tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_LEN]
                                     ? ptPosInfo->w : tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_LEN];
                        // 设置边界像素信息
                        ptPosInfo->byBadd = tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_BEGIN];  // 起始边界像素
                        ptPosInfo->byEadd = tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_END];    // 结束边界像素

                        // 智能边界像素分配：处理额外像素需求
                        if (ptPosInfo->byWadd)  // 存在额外像素需求
                        {
                            if (0 == ptPosInfo->byBadd && 0 == ptPosInfo->byEadd )
                            {
                                // 如果没有边界像素，将额外像素全部分配给结束边界
                                ptPosInfo->byEadd = ptPosInfo->byWadd;
                            }
                            else if (ptPosInfo->byBadd && 0 == ptPosInfo->byEadd )
                            {
                                // 如果有起始边界像素，剩余的分配给结束边界
                                ptPosInfo->byEadd = ptPosInfo->byWadd - ptPosInfo->byBadd;
                            }
                        }

                        // 执行实际的资源分配和位图更新
                        dwUniq = NvrSgwStreamUseMapUpdate(&g_atResUseMap[nChn], ptPosInfo);
                    }
                    break;  // 找到资源，立即退出内层循环
                }
            }

            // 清理候选资源信息，准备下一行的扫描
            mzero(tBitMapInfo);
        }

        // 快速退出：一旦找到合适资源，立即结束整个扫描过程
        if (bFind)
        {
            break;  // 退出外层循环
        }
    }
    
    // 函数退出调试信息：记录分配结果的完整信息
    if (g_nBitmapPrint > 0)
    {
        SGWPRINTIMP("<<<<< [CALL-%d] NvrSgwGetResFromBitMap EXIT: ow=%d result=%u bFind=%d x=%d y=%d final_w=%d final_h=%d\n",
          s_nCallCount,
          ptPosInfo ? ptPosInfo->ow : -1,
          dwUniq,                                    // 分配结果：唯一ID或0
          bFind,                                     // 是否找到合适资源
          ptPosInfo ? ptPosInfo->x : -1,            // 最终X坐标
          ptPosInfo ? ptPosInfo->y : -1,            // 最终Y坐标
          ptPosInfo ? ptPosInfo->w : -1,            // 最终宽度单位数
          ptPosInfo ? ptPosInfo->h : -1);           // 最终高度单位数
    }
    return dwUniq;  // 返回分配结果：成功时为唯一ID，失败时为0
}

/*
 * 释放资源到位图 - 资源回收的核心函数
 * 
 * @param nChn: 通道号
 * @param x: 起始X坐标（资源单位）
 * @param y: 起始Y坐标（资源单位）
 * @param nUnitLen: 宽度单位数
 * @param nDepth: 高度单位数
 * @return: 0（固定返回值）
 * 
 * 功能：将指定区域的资源状态从占用(0)恢复为可用(1)
 * 这是与NvrSgwGetResFromBitMap相对应的逆操作
 */
s32 NvrSgwFreeResToBitMap(s32 nChn, u16 x, u16 y, u16 nUnitLen, u16 nDepth)
{
    u32 dwMask = 0;

    // 生成释放掩码：与分配时使用相同的掩码计算方法
    dwMask = CHECK_MASK(nUnitLen, x);

    SGWPRINTIMP("free x:%2d, y:%2d, len:%d d:%d mask:%x\n", x, y, nUnitLen, nDepth, dwMask);

    // 调用位图更新函数执行实际释放：bUse=FALSE表示释放资源
    NvrSgwResBitMapUpdate(nChn, FALSE, dwMask, y, nDepth);
    return 0;
}

/*
 * 统计指定通道的可用资源总数 - 性能监控函数
 * 
 * @param nChn: 通道号
 * @return: 可用的资源单位总数
 * 
 * 算法：遍历32x12位图的每个位，统计值为1的位数
 * 时间复杂度：O(32×12) = O(384)
 */
s32 NvrSgwResMapGetAvailableNum(s32 nChn)
{
    s32 i = 0, j = 0;
    s32 nCount = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();  // 记录开始时间用于性能分析

    // 双重循环遍历整个位图
    for (j = 0; j < RES_BITMAP_MAX_NUM; j++)      // 遍历所有行
    {
        for (i = 1; i <= RES_BITMAP_LEN; i++)     // 遍历每行的所有位
        {
            // 检查每个位是否可用（值为1）
            if (MASK_GET_BIT(g_adwResMap[nChn][j], RES_BITMAP_LEN - i))
            {
                nCount++;  // 可用位计数器加1
            }
        }
    }
    // 性能统计：输出统计耗时
    SGWPRINTIMP("get available chn:%d take time %u ms\n", nChn, NvrSysGetCurTimeMSec() - timeIn);

    return nCount;  // 返回可用资源单位总数
}


void NvrSgwResMapShow(s32 nChn)
{
    if (nChn > 100)
    {
        s32 i = 0, j  = 0;
        SGWPRINTIMP(" g_adwResMap\n");
        for (j = 0; j < NVR_SGW_MAX_CHN_NUM; j++)
        {
            for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
            {
                SGWPRINTIMP(" g_adwResMap[%d][%-2d] 0x%8x\n", j, i, g_adwResMap[j][i]);
            }
        }
        SGWPRINTIMP(" g_dwAudDecIdMap 0x%x\n", g_dwAudDecIdMap);
        SGWPRINTIMP(" g_dwVidDecIdMap 0x%x  0x%x\n", g_adwVidDecIdMap[0], g_adwVidDecIdMap[1]);
        SGWPRINTIMP(" g_dwVidDecWinMap 0x%4x  0x%4x  0x%4x  0x%4x\n", g_adwVidDecWinMap[0], g_adwVidDecWinMap[1],
                g_adwVidDecWinMap[2], g_adwVidDecWinMap[3]);
    }
    else
    {
        NvrSgwShowResBitMap(nChn);
    }
}

void NvrSgwResMapShowAll(s32 nChn)
{
    s32 i = 0, j = 0, k = 0;

    OspPrintf(TRUE, FALSE, "%d   0                  10                  20                  30\n", nChn);

    for (k = 0; k < 4; k++)
    {
        for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
        {
            OspPrintf(TRUE, FALSE, "%2d: ", i);
            for (j = 0; j < RES_BITMAP_LEN; j++)
            {
                OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[k][i], RES_BITMAP_LEN - 1 - j));
            }

            OspPrintf(TRUE, FALSE, "\n");
        }
        OspPrintf(TRUE, FALSE, "\n");
    }

    NvrSgwShowAllStreamBitMap(0);
    NvrSgwShowAllStreamBitMap(1);
}

s32 NvrSgwGetAvInfoByUniq(u32 dwUniq, TNvrSgwResPosInfo *ptPosInfo)
{
    s32 i = 0, nChn = 0;
    BOOL bFind = 0;

    for (nChn = 0; nChn < NVR_SGW_MAX_CHN_NUM; nChn++)
    {
        for (i = 0; i < g_atResUseMap[nChn].nArrayCount; i++)
        {
            if (dwUniq == g_atResUseMap[nChn].atResPos[i].nUniq)
            {
                memcpy(ptPosInfo, &g_atResUseMap[nChn].atResPos[i], sizeof(g_atResUseMap[nChn].atResPos[i]));
                ptPosInfo->nVidDecId -= 1;
                ptPosInfo->nChnWinIndex -= 1;
                ptPosInfo->nAudDecId -= 1;
                bFind = TRUE;
                break;
            }
        }
    }

    return bFind  ? 1 : 0;
}

BOOL32 g_byManualPullRtmpTest = 0;

u32 NvrSgwStreamAdd(TNvrSgwResPosInfo *ptPosInfo)
{
    s32 i = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();
    u32 dwUniq = 0;
    s32 nChnB = 0, nChnE = 0;


    if (g_nBitmapPrint > 0)
    {
        SGWPRINTDBG("stream add uniq:%d w:%d h:%d vid:%d aud:%d prepare:%d wa:%d ow:%d\n",
                ptPosInfo->nUniq, ptPosInfo->w, ptPosInfo->h,
                ptPosInfo->nVidDecId, ptPosInfo->nAudDecId, ptPosInfo->byPrepare,
                ptPosInfo->byWadd, ptPosInfo->ow);
    }

    if ((0 == ptPosInfo->w || 0 == ptPosInfo->h) && 0 == ptPosInfo->nAudDecId)
    {
        return 0;
    }

    if (ptPosInfo->nAudDecId > 0)
    {
        nChnB = ((ptPosInfo->nAudDecId - 1) / (NVR_SGW_MAX_CHN_AUD_NUM * NVR_SGW_MAX_CHIP_CHN_NUM)) * NVR_SGW_MAX_CHIP_CHN_NUM;
        nChnE = nChnB + NVR_SGW_MAX_CHIP_CHN_NUM;
    }
    else
    {
        nChnB = 0;
        nChnE = nChnB + 4;
    }

	//这里优先根据音频资源进行分配,调试时可以缩小nChnB到nChnE的范围达到强制使用某物理光口的目的
	if(g_byManualPullRtmpTest && ptPosInfo->nAudDecId > 0)
	{
		nChnB = ptPosInfo->nAudDecId / NVR_SGW_MAX_CHN_AUD_NUM;  //0-3
		nChnE = nChnB + 1;
		g_byManualPullRtmpTest = 0;
	}

    for (i = nChnB; i < nChnE; i++)
    {
        ptPosInfo->nChn = i;
        if (g_nBitmapPrint > 3)
        {
            NvrSgwShowChnResBitMap(i);
        }
        dwUniq = NvrSgwGetResFromBitMap(ptPosInfo);
        if (dwUniq)
        {
            break;
        }
    }

    if (dwUniq)
    {
        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("calc take time %u ms\n",NvrSysGetCurTimeMSec() - timeIn);
        }
    }
    else
    {
        SGWPRINTERR("calc w:%d h:%d vid:%d aud:%d fail take time %u ms\n",
                ptPosInfo->w, ptPosInfo->h, ptPosInfo->nVidDecId, ptPosInfo->nAudDecId, NvrSysGetCurTimeMSec() - timeIn);
    }

    return dwUniq;
}

/*
 * 流资源删除函数 - 完整的资源回收和清理操作
 * 
 * @param dwUniq: 要删除的资源唯一标识符
 * 
 * 功能：根据唯一ID查找并删除对应的资源分配，执行完整的清理操作
 * 
 * 删除步骤：
 * 1. 全局搜索：在所有通道中查找匹配的唯一ID
 * 2. 资源回收：释放音频解码器、视频解码器、显示窗口等硬件资源
 * 3. 特殊像素清理：调用智能清理机制处理像素级资源
 * 4. 主位图释放：将占用的位图区域标记为可用
 * 5. 数组维护：从使用列表中移除该资源记录
 * 6. 芯片级清理：从芯片级流使用信息中移除对应记录
 * 
 * 关键设计：
 * - 二级清理：通道级 + 芯片级的双重清理确保完整性
 * - 智能像素处理：特殊像素清理会自动处理对应的主位图单元
 * - 数组压缩：删除元素后自动压缩数组，保持数据结构紧凑
 */
void NvrSgwStreamDel(u32 dwUniq)
{
    SGWPRINTIMP("free dwUniq 0x%x\n", dwUniq);
    TNvrSgwResUseInfo tChnResUseMap;      // 通道资源使用信息备份
    TNvrSgwResAllUseInfo tAllResUseMap;   // 芯片流使用信息备份
    s32 i = 0, nChn = 0;
    BOOL bDel = FALSE;                    // 删除成功标志

    mzero(tChnResUseMap);

    // 第一阶段：全局搜索目标资源
    // 遍历所有通道，查找匹配的唯一ID
    for (nChn = 0; nChn < NVR_SGW_MAX_CHN_NUM; nChn++)
    {
        // 在当前通道的所有已分配资源中查找
        for (i = 0; i < g_atResUseMap[nChn].nArrayCount; i++)
        {
            // 找到匹配的唯一ID
            if (dwUniq == g_atResUseMap[nChn].atResPos[i].nUniq)
            {
                // 备份当前通道的资源使用信息（用于数组操作）
                memcpy(&tChnResUseMap, &g_atResUseMap[nChn], sizeof(tChnResUseMap));

                // 第二阶段：硬件资源回收
                // 回收音频解码器资源
                if (g_atResUseMap[nChn].atResPos[i].nAudDecId > 0)
                {
                    g_atResUseMap[nChn].nArrayAudCount--;  // 音频资源计数减1
                    // 释放音频解码器ID到位图：FALSE表示释放（置1）
                    NvrSgwAudDecIdBitMapUpdate(FALSE, g_atResUseMap[nChn].atResPos[i].nAudDecId);
                }

                // 回收视频解码器资源
                if (g_atResUseMap[nChn].atResPos[i].nVidDecId > 0)
                {
                    // 释放视频解码器ID到位图：FALSE表示释放（置1）
                    NvrSgwVidDecIdBitMapUpdate(nChn, FALSE, g_atResUseMap[nChn].atResPos[i].nVidDecId);
                }

                // 回收视频显示窗口资源
                if (g_atResUseMap[nChn].atResPos[i].nChnWinIndex > 0)
                {
                    // 释放显示窗口ID到位图：FALSE表示释放（置1）
                    NvrSgwVidWinIdBitMapUpdate(nChn, FALSE, g_atResUseMap[nChn].atResPos[i].nChnWinIndex);
                }

                // 第三阶段：智能像素级清理
                // 参数0表示释放操作，会触发智能清理机制：
                // 1. 清理特殊像素映射
                // 2. 检查并自动释放对应的主位图单元（如果该单元完全空闲）
                NvrSgwSpecMapArrUpdate(&g_atResUseMap[nChn].atResPos[i], 0);

                SGWPRINTDBG("free dwUniq nChn %d vid %d, aud %d, i:%d(%d) diff:%d\n", nChn, 
                        g_atResUseMap[nChn].atResPos[i].nVidDecId,
                        g_atResUseMap[nChn].atResPos[i].nAudDecId, i, 
                        g_atResUseMap[nChn].nArrayCount,
                        g_atResUseMap[nChn].nArrayCount - 1 - i);

                // 第四阶段：数组维护 - 删除元素并压缩数组
                if ((i + 1) != g_atResUseMap[nChn].nArrayCount)  // 如果不是最后一个元素
                {
                    // 将后续元素前移，覆盖要删除的元素
                    memcpy(&g_atResUseMap[nChn].atResPos[i], &tChnResUseMap.atResPos[i + 1],
                            sizeof(TNvrSgwResPosInfo) * (g_atResUseMap[nChn].nArrayCount - 1 - i));
                }
                g_atResUseMap[nChn].nArrayCount--;  // 数组元素计数减1

                // 第五阶段：主位图释放
                // 将占用的2D位图区域标记为可用（置1）
                NvrSgwFreeResToBitMap(nChn, tChnResUseMap.atResPos[i].x, tChnResUseMap.atResPos[i].y,
                        tChnResUseMap.atResPos[i].w, tChnResUseMap.atResPos[i].h);
                        
                bDel = TRUE;  // 标记删除成功
                break;        // 退出内层循环
            }
        }

        // 第六阶段：芯片级流信息清理
        if (bDel)  // 如果在通道级找到并删除了资源
        {
            // 获取对应芯片的流使用信息指针
            TNvrSgwResAllUseInfo *ptStream = &g_atStreamUseInfo[nChn / 2];
            
            // 在芯片级流使用信息中查找并删除对应记录
            for (i = 0; i < ptStream->nArrayCount; i++)
            {
                // 备份芯片级流信息（用于数组操作）
                memcpy(&tAllResUseMap, ptStream, sizeof(tAllResUseMap));

                if (dwUniq == ptStream->atResPos[i].nUniq)  // 找到匹配的记录
                {
                    // 删除元素并压缩数组：将后续元素前移
                    memcpy(&ptStream->atResPos[i], &tAllResUseMap.atResPos[i + 1], 
                           sizeof(tAllResUseMap.atResPos[0]) * (ptStream->nArrayCount - 1 - i));
                    ptStream->nArrayCount--;  // 芯片级流计数减1
                }
            }

            SGWPRINTIMP("del dwUniq 0x%x succ\n", dwUniq);
            break;  // 删除成功，退出外层通道循环
        }
    }

    // 删除结果处理
    if (FALSE == bDel)
    {
        SGWPRINTERR("del dwUniq 0x%x fail\n", dwUniq);  // 未找到对应的资源记录
    }
    
    /* 函数总结：
     * NvrSgwStreamDel实现了完整的六阶段资源回收流程：
     * 1. 全局搜索 -> 2. 硬件资源回收 -> 3. 智能像素清理 -> 
     * 4. 数组压缩 -> 5. 主位图释放 -> 6. 芯片级清理
     * 
     * 关键特性：
     * - 原子性：整个删除过程要么完全成功，要么完全失败
     * - 完整性：通道级和芯片级的双重清理确保无遗漏
     * - 智能化：特殊像素清理会自动处理相关的主位图单元
     * - 紧凑性：自动压缩数组，保持数据结构的连续性
     */
}

s32 NvrSgwStreamDecApply(TNvrSgwResPosInfo *ptPosInfo)
{
    s32 nRet = 0;
    s32 dwUniq = 0;
    s32 nAudDecId = 0;
    pthread_mutex_lock(&res_apply_lock);

    if (0 == ptPosInfo->nUniq)
    {
        if (ptPosInfo->nAudDecId >= 0)
        {
            nAudDecId = NvrSgwAudDecMapCheck(ptPosInfo->nAudDecId);
            if (nAudDecId)
            {
                ptPosInfo->nAudDecId = nAudDecId;
            }
            else
            {
                ptPosInfo->nAudDecId = -1;
                pthread_mutex_unlock(&res_apply_lock);
                return dwUniq;
            }
        }
        else
        {
            ptPosInfo->nAudDecId = 0;
        }
        ///< 传入uniq为0，则表示使用新分配uniq，直接申请，对于音视频id需要特殊处理
        ptPosInfo->ow = ptPosInfo->w;
        if (ptPosInfo->w > 1200 && ptPosInfo->w <= 1280)
        {
            ptPosInfo->w = GET_RES_WIDTH_BASE(ptPosInfo->ow);
            ptPosInfo->byWadd =  GET_RES_WIDTH_LEFT(ptPosInfo->ow);
        }
        else
        {
            ptPosInfo->w = GET_RES_WIDTH_UNIT(ptPosInfo->ow);
        }
        ptPosInfo->h = GET_RES_HEIGHT_UNIT(ptPosInfo->h);

        dwUniq =  NvrSgwStreamAdd(ptPosInfo);
    }
    else
    {
        ///< 传入uniq非0，则表示使用已于的uniq，直接获取
        dwUniq = ptPosInfo->nUniq;
    }


    if (dwUniq)
    {
        nRet = NvrSgwGetAvInfoByUniq(dwUniq, ptPosInfo);
        if (nRet)
        {
            SGWPRINTDBG("\nstream add uniq:%d x:%d y:%d w:%d h:%d \n"
                    "ow:%d wa:%d b:%d e:%d\n"
                    "chn:%d vid:%d aud:%d win:%d\n",
                    ptPosInfo->nUniq, ptPosInfo->x, ptPosInfo->y, ptPosInfo->w, ptPosInfo->h,
                    ptPosInfo->ow, ptPosInfo->byWadd, ptPosInfo->byBadd, ptPosInfo->byEadd,
                    ptPosInfo->nChn, ptPosInfo->nVidDecId, ptPosInfo->nAudDecId, ptPosInfo->nChnWinIndex);

            NvrSgwCheckAllRects(ptPosInfo->nChn);
        }
    }
    else
    {
        ptPosInfo->nAudDecId = nAudDecId - 1;
    }

    pthread_mutex_unlock(&res_apply_lock);

    return dwUniq;
}

s32 NvrSgwStreamDecAudPrepare(s32 nDecID)
{

    if (g_nBitmapPrint > 2)
    {
        SGWPRINTDBG("%x %x %x %x \n", g_dwAudDecIdMap & 0xff, (g_dwAudDecIdMap >> 8) & 0xff,
            (g_dwAudDecIdMap >> 16) & 0xff, (g_dwAudDecIdMap >> 24) & 0xff);
    }
    if (MASK_GET_BIT(g_dwAudDecIdMap, nDecID))
    {
        return nDecID + 1;
    }
    else
    {
        return 0;
    }
}

s32 NvrSgwStreamDecApplyTest(s32 w, s32 h, s32 nAudDecId)
{
    s32 nRet = 0;
    s32 dwUniq = 0;
    TNvrSgwResPosInfo tPos;

    mzero(tPos);

    pthread_mutex_lock(&res_apply_lock);

    tPos.ow = w;
    if (w > 1200 && w <= 1280)
    {
        tPos.w = GET_RES_WIDTH_BASE(w);
        tPos.byWadd =  GET_RES_WIDTH_LEFT(w);
    }
    else
    {
        tPos.w = GET_RES_WIDTH_UNIT(w);
    }
    tPos.h = GET_RES_HEIGHT_UNIT(h);
    tPos.nAudDecId = nAudDecId;
    dwUniq = NvrSgwStreamAdd(&tPos);

    if (dwUniq)
    {
        nRet = NvrSgwGetAvInfoByUniq(dwUniq, &tPos);
        if (nRet)
        {
            SGWPRINTDBG("\nstream add uniq:%d x:%d y:%d w:%d h:%d \n"
                    "ow:%d wa:%d b:%d e:%d\n"
                    "chn:%d vid:%d aud:%d win:%d\n",
                    tPos.nUniq, tPos.x, tPos.y, tPos.w, tPos.h,
                    tPos.ow, tPos.byWadd, tPos.byBadd, tPos.byEadd,
                    tPos.nChn, tPos.nVidDecId, tPos.nAudDecId, tPos.nChnWinIndex);
        }
    }

    pthread_mutex_unlock(&res_apply_lock);

    return dwUniq;
}

s32 NvrSgwStreamDecFree(s32 nUniq)
{

    pthread_mutex_lock(&res_apply_lock);

    if (nUniq)
    {
        NvrSgwStreamDel(nUniq);
    }

    pthread_mutex_unlock(&res_apply_lock);

    return 0;
}

/*
 * ==================== 资源状态备份与恢复系统 ====================
 * 用于资源重排时的安全回滚机制
 */

/*
 * 芯片资源状态备份函数
 * 
 * @param nChip: 芯片号（当前未使用，备份所有芯片状态）
 * 
 * 功能：将当前所有资源管理状态保存到备份数组
 * 用途：在资源重排前保存稳定状态，失败时可快速恢复
 */
void NvrSgwResChipCopy(s32 nChip)
{
    // 备份通道级资源使用映射
    memcpy(g_atResUseMap_bk, g_atResUseMap, sizeof(g_atResUseMap));
    // 备份芯片级流使用信息
    memcpy(g_atStreamUseInfo_bk, g_atStreamUseInfo, sizeof(g_atStreamUseInfo));
    // 备份主位图资源映射
    memcpy(g_adwResMap_bk, g_adwResMap, sizeof(g_adwResMap));
    // 备份音频解码器分配位图
    g_dwAudDecIdMap_bk = g_dwAudDecIdMap;
    // 备份视频解码器分配位图
    memcpy(g_adwVidDecIdMap_bk, g_adwVidDecIdMap, sizeof(g_adwVidDecIdMap));
    // 备份视频窗口分配位图
    memcpy(g_adwVidDecWinMap_bk, g_adwVidDecWinMap, sizeof(g_adwVidDecWinMap));
    // 备份特殊像素级资源映射
    memcpy(g_adwResMapSpe_bk, g_adwResMapSpe, sizeof(g_adwResMapSpe));
}

/*
 * 芯片资源状态恢复函数
 * 
 * @param nChip: 芯片号（当前未使用，恢复所有芯片状态）
 * 
 * 功能：从备份数组恢复之前的资源管理状态
 * 用途：当资源重排失败时，快速回滚到重排前的稳定状态
 */
void NvrSgwResChipResume(s32 nChip)
{
    // 恢复通道级资源使用映射
    memcpy(g_atResUseMap, g_atResUseMap_bk, sizeof(g_atResUseMap));
    // 恢复芯片级流使用信息
    memcpy(g_atStreamUseInfo, g_atStreamUseInfo_bk, sizeof(g_atStreamUseInfo));
    // 恢复主位图资源映射
    memcpy(g_adwResMap, g_adwResMap_bk, sizeof(g_adwResMap));
    // 恢复音频解码器分配位图
    g_dwAudDecIdMap = g_dwAudDecIdMap_bk;
    // 恢复视频解码器分配位图
    memcpy(g_adwVidDecIdMap, g_adwVidDecIdMap_bk, sizeof(g_adwVidDecIdMap));
    // 恢复视频窗口分配位图
    memcpy(g_adwVidDecWinMap, g_adwVidDecWinMap_bk, sizeof(g_adwVidDecWinMap));
    // 恢复特殊像素级资源映射
    memcpy(g_adwResMapSpe, g_adwResMapSpe_bk, sizeof(g_adwResMapSpe));
}

/*
 * 清空所有资源分配状态 - 系统重置函数
 * 
 * 功能：将整个资源管理系统恢复到初始状态
 * - 清空所有使用记录（设置为0）
 * - 重置所有位图为全可用状态（设置为0xFF）
 */
void NvrSgwResDelAll()
{
    // 清空资源使用记录
    mzero(g_atResUseMap);           // 通道级使用记录
    mzero(g_atStreamUseInfo);       // 芯片级流使用记录
    mzero(g_adwResMapSpe);         // 特殊像素级映射

    // 重置所有位图为全可用状态（位图中1表示可用）
    memset(g_adwResMap, 0xff, sizeof(g_adwResMap));                // 主位图
    memset(&g_dwAudDecIdMap, 0xff, sizeof(g_dwAudDecIdMap));      // 音频解码器位图
    memset(g_adwVidDecIdMap, 0xff, sizeof(g_adwVidDecIdMap));     // 视频解码器位图
    memset(g_adwVidDecWinMap, 0xff, sizeof(g_adwVidDecWinMap));   // 视频窗口位图
}

/*
 * 清空指定芯片的资源分配状态
 * 
 * @param nChip: 芯片号
 * 
 * 功能：选择性重置单个芯片的资源状态
 * 用途：芯片级的资源清理，不影响其他芯片
 */
void NvrSgwResChipDel(s32 nChip)
{
    s32 i = 0;
    s32 nChn = nChip * NVR_SGW_MAX_CHIP_CHN_NUM;  // 计算芯片对应的起始通道号

    // 清空芯片级流使用信息
    mzero(g_atStreamUseInfo[nChip]);

    // 清空该芯片所有通道的资源状态
    for (i = nChn; i < nChn + NVR_SGW_MAX_CHIP_CHN_NUM; i++)
    {
        mzero(g_atResUseMap[i]);                                        // 清空通道使用记录
        memset(&g_adwResMap[i], 0xFF, sizeof(g_adwResMap[0]));         // 重置主位图为全可用
        memset(&g_adwVidDecWinMap[i], 0xFF, sizeof(g_adwVidDecWinMap[0])); // 重置窗口位图为全可用
        mzero(g_adwResMapSpe[i]);                                      // 清空特殊像素映射
    }
}

/*
 * ==================== 芯片级资源重排算法 ====================
 * 动态碎片整理和资源优化分配的核心实现
 * 
 * @param nChip: 目标芯片号
 * @param ptReqPos: 新的资源请求（触发重排的原因）
 * @return: 成功返回分配的唯一ID，失败返回0
 * 
 * 算法核心思想："先清空，再重排，最后分配新请求"
 * 
 * 三阶段重排策略：
 * 1. 状态保存：备份当前资源状态，清空芯片资源
 * 2. 逆序重排：从最大资源开始，逐个重新分配已有流
 * 3. 新请求分配：所有已有流重排成功后，尝试分配新请求
 * 
 * 失败回滚机制：
 * - 任何阶段失败都会立即恢复到重排前的稳定状态
 * - 保证系统的可靠性和资源状态的一致性
 * 
 * 重排优势：
 * - 消除资源碎片：通过重新排列实现更紧凑的资源布局
 * - 提高利用率：为新请求腾出更大的连续空间
 * - 动态优化：根据当前负载情况自动调整资源分布
 */
s32 NvrSgwStreamChipArrange(s32 nChip, TNvrSgwResPosInfo *ptReqPos)
{
    s32 i = 0, j = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();                     // 性能计时起点
    s32 nChn = nChip * NVR_SGW_MAX_CHIP_CHN_NUM;            // 芯片起始通道号
    s32 dwUniq = 0;                                          // 最终分配结果
    BOOL bSUcc = FALSE;                                      // 重排成功标志
    TNvrSgwResAllUseInfo tUseMap;                           // 备份的流使用信息

    // 详细调试信息：记录重排开始时的系统状态
    SGWPRINTIMP("######### NvrSgwStreamChipArrange START: chip=%d reqPos(ow=%d w=%d h=%d) existing_streams=%d #########\n",
          nChip,
          ptReqPos ? ptReqPos->ow : -1,
          ptReqPos ? ptReqPos->w : -1,
          ptReqPos ? ptReqPos->h : -1,
          g_atStreamUseInfo[nChip].nArrayCount);

    // 线程安全保护：整个重排过程需要原子性执行
    pthread_mutex_lock(&res_apply_lock);
    
    // 第一阶段：状态保存和清空
    if (g_atStreamUseInfo[nChip].nArrayCount > 0)  // 如果存在已分配的流
    {
        // 备份当前芯片的所有流使用信息
        memcpy(&tUseMap, &g_atStreamUseInfo[nChip], sizeof(g_atStreamUseInfo[0]));

        // 保存完整的系统状态到备份数组（用于失败回滚）
        NvrSgwResChipCopy(nChip);
        
        // 清空芯片的所有资源分配状态
        NvrSgwResChipDel(nChip);

        // 第二阶段：逆序重排已有流
        // 关键策略：从最后一个（通常是最大的）资源开始重排
        // 逆序的优势：大资源优先分配，小资源填充空隙，减少碎片
        for (i = tUseMap.nArrayCount - 1; i >= 0; i--)
        {
            // 详细重排进度信息
            SGWPRINTIMP("### Rearranging stream [%d/%d]: uniq=%u ow=%d original(w=%d h=%d x=%d y=%d)\n",
                  (tUseMap.nArrayCount - 1 - i), tUseMap.nArrayCount - 1,
                  tUseMap.atResPos[i].nUniq,
                  tUseMap.atResPos[i].ow,
                  tUseMap.atResPos[i].w,
                  tUseMap.atResPos[i].h,
                  tUseMap.atResPos[i].x,
                  tUseMap.atResPos[i].y);

            bSUcc = FALSE;  // 重置当前流的重排成功标志
            
            // 尝试在芯片的所有通道中重新分配当前流
            for (j = nChn; j < nChn + NVR_SGW_MAX_CHIP_CHN_NUM; j++)
            {
                // 重置流的分配参数为重排状态
                tUseMap.atResPos[i].nChn = j;                    // 尝试新的通道
                tUseMap.atResPos[i].nChnWinIndex = 0;           // 重置窗口索引
                
                // 处理带有额外像素需求的资源
                if (tUseMap.atResPos[i].byWadd)
                {
                    // 重置宽度为基础单位数，清空边界像素
                    tUseMap.atResPos[i].w = GET_RES_WIDTH_BASE(tUseMap.atResPos[i].ow);
                    tUseMap.atResPos[i].byBadd = 0;
                    tUseMap.atResPos[i].byEadd = 0;
                }

                // 可选的位图显示（高级调试模式）
                if (g_nBitmapPrint > 2)
                {
                    SGWPRINTIMP("before arrange show bitmap \n");
                    NvrSgwShowChnResBitMap(j);
                }
                
                // 核心重分配：调用主分配算法重新分配当前流
                dwUniq = NvrSgwGetResFromBitMap(&tUseMap.atResPos[i]);
                
                // 重分配结果记录
                SGWPRINTIMP("    Channel %d result: %s (dwUniq=%u)\n", j, dwUniq ? "SUCCESS" : "FAILED", dwUniq);
                
                if (dwUniq)  // 如果在当前通道分配成功
                {
                    bSUcc = TRUE;
                    break;  // 跳出通道尝试循环
                }
            }

            // 关键失败处理：如果任何一个已有流无法重新分配
            if (!bSUcc)
            {
                SGWPRINTERR("### REARRANGE FAILED: stream [%d] uniq=%u ow=%d - RESTORE ALL\n",
                      i, tUseMap.atResPos[i].nUniq, tUseMap.atResPos[i].ow);
                // 立即恢复到重排前的状态，保证系统稳定性
                NvrSgwResChipResume(nChip);
                break;  // 退出重排循环
            }
        }
    }

    // 第三阶段：分配新请求
    if (bSUcc)  // 只有在所有已有流都重排成功后才尝试新分配
    {
        SGWPRINTIMP("### All existing streams rearranged successfully, now allocating NEW REQUEST:\n");
        SGWPRINTIMP("    Original request: ow=%d w=%d h=%d\n", ptReqPos->ow, ptReqPos->w, ptReqPos->h);

        // 设置预分配模式：仅测试是否可以分配，不实际占用资源
        ptReqPos->byPrepare = 1;
        ptReqPos->w = GET_RES_WIDTH_UNIT(ptReqPos->w);       // 转换为资源单位
        ptReqPos->h = GET_RES_HEIGHT_UNIT(ptReqPos->h);      // 转换为资源单位

        // 尝试为新请求分配资源
        dwUniq = NvrSgwStreamAdd(ptReqPos);
        if (dwUniq)
        {
            SGWPRINTIMP("### NEW REQUEST ALLOCATED SUCCESS, uniq:%d\n", dwUniq);
        }
        else
        {
            SGWPRINTIMP("### NEW REQUEST ALLOCATED FAILED, RESTORE ALL\n");
            // 新请求分配失败：重排虽然成功，但无法满足新需求
            // 恢复原状态，避免无价值的重排操作
            NvrSgwResChipResume(nChip);
        }
    }
    
    // 性能统计和函数退出
    SGWPRINTIMP("arrage chip:%d over, take time %u ms\n", nChip, NvrSysGetCurTimeMSec() - timeIn);
    pthread_mutex_unlock(&res_apply_lock);
    return dwUniq;  // 返回最终分配结果
}

s32 NvrSgwStreamArrange(TNvrSgwResPosInfo *ptPosInfo, s32 *pnChip)
{
    s32 nRet = 0;
    s32 nChipB = 0, nChipE = 0, i = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();
    TNvrSgwResPosInfo tTmpPosInfo;

    if (-1 != ptPosInfo->nAudDecId)
    {
        nChipB = ptPosInfo->nAudDecId / 16;
        nChipE = nChipB + 1;
    }
    else
    {
        nChipB = 0;
        nChipE = nChipB + 2;
    }

    for (i = nChipB; i < nChipE; i++)
    {
        memcpy(&tTmpPosInfo, ptPosInfo, sizeof(tTmpPosInfo));
        nRet = NvrSgwStreamChipArrange(i, &tTmpPosInfo);
        if (nRet)
        {
            *pnChip = i;
            break;
        }
    }

    if (nRet)
    {
        NvrSgwStreamDecApply(ptPosInfo);
    }

    SGWPRINTIMP("arrage  take time %u ms\n", NvrSysGetCurTimeMSec() - timeIn);

    return nRet;
}

void NvrSgwStreamArrangeTest(s32 nAud)
{
    TNvrSgwResPosInfo tPosInfo;
    s32 nChip = 0;
    tPosInfo.w = 1000;
    tPosInfo.h = 600;
    tPosInfo.nAudDecId = nAud > 0 ? (nAud - 1) : -1;

    NvrSgwStreamArrange(&tPosInfo, &nChip);
}

s32 NvrSgwStreamResume(s32 nChip)
{
    NvrSgwResChipResume(nChip);
    return 0;
}

void NvrSgwResUse(u16 x, u16 y, u16 w, u16 h, s32 nChn)
{
    u32 dwMask = 0;

    dwMask = CHECK_MASK(w, x);
    NvrSgwResBitMapUpdate(nChn, TRUE, dwMask, y, h);

}

void NvrSgwResUseChn(s32 nChn)
{
    NvrSgwResUse(0, 0, 32, 12, nChn);
}

void NvrSgwResDbg(s32 nLevel)
{
    SGWPRINTIMP("print level %d --> %d\n", g_nBitmapPrint, nLevel);
    g_nBitmapPrint = nLevel;
}

void NvrSgwResShowSpeArr(s32 n)
{
    s32 i = 0, j = 0, k = 0;

    i = n;
    for (j = 0; j < RES_BITMAP_MAX_NUM; j++)
    {
        OspPrintf(TRUE, FALSE, "%2d-%2d  ", i, j);
        for (k = 0; k < 32; k++)
        {
            if (g_adwResMapSpe[i][j][k])
            {
                OspPrintf(TRUE, FALSE, "[%2d=0x%2x] ", k, g_adwResMapSpe[i][j][k]);
            }
        }
        OspPrintf(TRUE, FALSE, "\n");
    }
}

static BOOL bAutoTestRun = TRUE;
#define my_random(x) (rand()%(x))
#define min_res_w   (720)
#define min_res_h   (576)
#define GET_RES_ALIGN(n, m)       (n + n % m)

#define arr_max     (8)

u32 adwRes[arr_max][2] = {{3840, 2160}, {2560, 1920}, {1920, 1080}, {1280, 720},
                    {960, 540}, {720, 576}, {704, 576}, {640, 480}};

void NvrSgwCalcTask()
{
    s32 nResW = 0, nResH = 0, nAudId = -1;
    TNvrSgwResPosInfo tReqPosInfo;
    s32 nRet = 0, nChip = 0, nRand = 0, nUniq = 0;

    while (bAutoTestRun)
    {
        mzero(tReqPosInfo);

        srand((int)time(0));
        nRand = my_random(arr_max);
        nResW = adwRes[nRand][0];
        nResH = adwRes[nRand][1];

        SGWPRINTIMP("===auto test res  %-4d x %-4d aud:%d \n", nResW, nResH, nAudId);

        tReqPosInfo.w = nResW;
        tReqPosInfo.h = nResH;
        tReqPosInfo.nAudDecId = -1;

        NvrSgwStreamDecApply(&tReqPosInfo);
        if (0 == tReqPosInfo.nUniq)
        {
            SGWPRINTIMP("nomal calc err and arrange\n");
            tReqPosInfo.w = nResW;
            tReqPosInfo.h = nResH;
            tReqPosInfo.nAudDecId = -1;

            nRet = NvrSgwStreamArrange(&tReqPosInfo, &nChip);
            SGWPRINTIMP("arrange %s \n", nRet ? "succ" : "fail");
            if (0 == nRet)
            {
                s32 nChn = 0, nIndex = 0;
                while (1)
                {
                    srand((int)time(0) + nResW);
                    nChn = my_random(4);
                    OspPrintf(TRUE, FALSE, "del chn:%d ", nChn);
                    if (g_atResUseMap[nChn].nArrayCount)
                    {
                        if (1 == g_atResUseMap[nChn].nArrayCount)
                        {
                            nUniq = g_atResUseMap[nChn].atResPos[0].nUniq;
                            OspPrintf(TRUE, FALSE, "del index:%d\n", 1);
                        }
                        else
                        {
                            srand((int)time(0) + nChn);
                            nIndex = my_random(g_atResUseMap[nChn].nArrayCount);
                            nUniq = g_atResUseMap[nChn].atResPos[nIndex].nUniq;
                            OspPrintf(TRUE, FALSE, "del index:%d\n", nIndex);
                        }
                        break;
                    }
                }
                ///< free
                NvrSgwStreamDecFree(nUniq);
            }
        }
        if (tReqPosInfo.nUniq)
        {
            NvrSgwShowChnResBitMap(tReqPosInfo.nChn);
        }
        SGWPRINTIMP("===auto test res  %-4d x %-4d aud:%d over\n", nResW, nResH, nAudId);
        sleep(10);
    }

    OsApi_TaskExit();
    return ;
}

void NvrSgwResAutoCalc(s32 n)
{
    if (n > 1)
    {
        bAutoTestRun = FALSE;
        SGWPRINTIMP("NvrSgwCalcTask exit\n");
        NvrSgwResDbg(1);
    }
    else
    {
        NvrSgwResDbg(4);
        bAutoTestRun = TRUE;
        if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSgwCalcTask, "SgwAuroCalcDeal", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
        {
            SGWFLASHERR("NvrSgwCalcTask create failed\n");
        }
    }
}

s32 NvrSgwResBitMapInit()
{
    memset(g_adwResMap, 0xffffffff, sizeof(g_adwResMap));

    pthread_mutex_init(&res_apply_lock, NULL);

    OsApi_RegCommandEx( "cas", (void *)NvrSgwResMapShow, "dec res show  ", "i");
    OsApi_RegCommandEx( "casa", (void *)NvrSgwResMapShowAll, "dec res show  ", "i");
    OsApi_RegCommandEx( "caa", (void *)NvrSgwStreamDecApplyTest, "dec res calc   ", "iii");
    OsApi_RegCommandEx( "cad", (void *)NvrSgwStreamDel, "dec res del   ", "i");
    OsApi_RegCommandEx( "cada", (void *)NvrSgwResDelAll, "dec res del all  ", "i");
    OsApi_RegCommandEx( "carr", (void *)NvrSgwStreamArrangeTest, "res arrange   ", "i");
    OsApi_RegCommandEx( "cause", (void *)NvrSgwResUse, "dec res calc   ", "iiiii");
    OsApi_RegCommandEx( "cadbg", (void *)NvrSgwResDbg, "res calc print level   ", "i");
    OsApi_RegCommandEx( "caspe", (void *)NvrSgwResShowSpeArr, "res print spe arr   ", "i");
    OsApi_RegCommandEx( "casauto", (void *)NvrSgwResAutoCalc, "auto test calc   ", "i");
    OsApi_RegCommandEx( "causechn", (void *)NvrSgwResUseChn, "dec res calc   ", "i");
    return 0;
}
